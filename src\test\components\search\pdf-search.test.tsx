import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import PDFSearch from '@/components/search/pdf-search'

describe('PDFSearch - Consolidated Component', () => {
  const defaultProps = {
    searchText: '',
    onSearchChange: vi.fn(),
    onClose: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Basic Search Functionality', () => {
    it('renders search input with current text', () => {
      render(<PDFSearch {...defaultProps} searchText="test query" />)
      
      const searchInput = screen.getByDisplayValue('test query')
      expect(searchInput).toBeInTheDocument()
    })

    it('calls onSearchChange when input changes', async () => {
      const user = userEvent.setup()
      const onSearchChange = vi.fn()

      render(<PDFSearch {...defaultProps} onSearchChange={onSearchChange} />)

      const searchInput = screen.getByRole('textbox')
      await user.type(searchInput, 'n')

      expect(onSearchChange).toHaveBeenCalledWith('n')
    })

    it('calls onClose when close button is clicked', async () => {
      const user = userEvent.setup()
      const onClose = vi.fn()

      render(<PDFSearch {...defaultProps} onClose={onClose} />)

      // The close button is rendered as a button with X icon
      const closeButton = screen.getByRole('button')
      await user.click(closeButton)

      expect(onClose).toHaveBeenCalled()
    })
  })

  describe('Enhanced Search Features', () => {
    it('displays search options when available', async () => {
      const user = userEvent.setup()
      render(
        <PDFSearch
          {...defaultProps}
          variant="enhanced"
          pdfDocument={{} as any}
          numPages={10}
          onPageSelect={vi.fn()}
          searchOptions={{ caseSensitive: false, wholeWords: true }}
        />
      )

      // Search options are hidden by default, need to click to expand
      const searchOptionsButton = screen.getByText(/search options/i)
      await user.click(searchOptionsButton)

      // Should show search options UI after expanding
      expect(screen.getByRole('checkbox', { name: /case sensitive/i })).toBeInTheDocument()
      expect(screen.getByRole('checkbox', { name: /whole words/i })).toBeInTheDocument()
    })

    it('handles search options changes', async () => {
      const user = userEvent.setup()
      const onSearchOptionsChange = vi.fn()

      render(
        <PDFSearch
          {...defaultProps}
          variant="enhanced"
          pdfDocument={{} as any}
          numPages={10}
          onPageSelect={vi.fn()}
          searchOptions={{ caseSensitive: false, wholeWords: false }}
          onSearchOptionsChange={onSearchOptionsChange}
        />
      )

      // First expand the search options
      const searchOptionsButton = screen.getByText(/search options/i)
      await user.click(searchOptionsButton)

      const caseSensitiveCheckbox = screen.getByRole('checkbox', { name: /case sensitive/i })
      await user.click(caseSensitiveCheckbox)

      expect(onSearchOptionsChange).toHaveBeenCalledWith({ caseSensitive: true, wholeWords: false })
    })

    it('displays search results when provided', () => {
      const searchResults = [
        {
          pageIndex: 0,
          textItems: [
            { str: 'test result', matches: [], transform: [], width: 100, height: 20, itemIndex: 0 }
          ]
        },
        {
          pageIndex: 1,
          textItems: [
            { str: 'another result', matches: [], transform: [], width: 100, height: 20, itemIndex: 1 }
          ]
        }
      ]

      render(
        <PDFSearch
          {...defaultProps}
          searchResults={searchResults}
          variant="enhanced"
          pdfDocument={{} as any}
          numPages={10}
          onPageSelect={vi.fn()}
        />
      )

      // The component shows results count in the navigation area
      expect(screen.getByText(/1 of 2/i)).toBeInTheDocument()
    })

    it('handles page navigation from search results', async () => {
      const user = userEvent.setup()
      const onPageSelect = vi.fn()
      const searchResults = [
        {
          pageIndex: 0,
          textItems: [
            { str: 'test result', matches: [], transform: [], width: 100, height: 20, itemIndex: 0 }
          ]
        }
      ]

      render(
        <PDFSearch
          {...defaultProps}
          searchResults={searchResults}
          onPageSelect={onPageSelect}
          variant="enhanced"
          pdfDocument={{} as any}
          numPages={10}
        />
      )

      // In enhanced mode, search results are shown in a list below
      // Look for the result item that can be clicked
      const resultItem = screen.getByText(/test result/i)
      await user.click(resultItem)

      expect(onPageSelect).toHaveBeenCalledWith(1)
    })
  })

  describe('Search Variants', () => {
    it('renders simple variant correctly', () => {
      render(<PDFSearch {...defaultProps} variant="simple" />)

      // Simple variant should have basic search input
      expect(screen.getByRole('textbox')).toBeInTheDocument()
      // Simple variant doesn't show search options
      expect(screen.queryByText(/search options/i)).not.toBeInTheDocument()
    })

    it('renders enhanced variant with additional features', () => {
      render(
        <PDFSearch
          {...defaultProps}
          variant="enhanced"
          pdfDocument={{} as any}
          numPages={10}
          onPageSelect={vi.fn()}
        />
      )

      // Enhanced variant should have search options
      expect(screen.getByRole('textbox')).toBeInTheDocument()
      expect(screen.getByText(/search options/i)).toBeInTheDocument()
    })

    it('renders unified variant with full functionality', () => {
      render(
        <PDFSearch
          {...defaultProps}
          variant="unified"
          searchResults={[]}
        />
      )

      // Unified variant should have all features
      expect(screen.getByRole('textbox')).toBeInTheDocument()
      expect(screen.getByText(/search options/i)).toBeInTheDocument()
      // Results section appears when there are search results
      expect(screen.getByText(/search in pdf/i)).toBeInTheDocument()
    })
  })

  describe('Legacy API Compatibility', () => {
    it('supports legacy onSearch callback', async () => {
      const user = userEvent.setup()
      const onSearch = vi.fn()
      
      render(<PDFSearch {...defaultProps} onSearch={onSearch} />)
      
      const searchInput = screen.getByRole('textbox')
      await user.type(searchInput, 'legacy search{enter}')
      
      expect(onSearch).toHaveBeenCalledWith('legacy search')
    })

    it('supports legacy navigation callbacks', async () => {
      const user = userEvent.setup()
      const onNavigateResults = vi.fn()

      render(
        <PDFSearch
          {...defaultProps}
          onNavigateResults={onNavigateResults}
          searchResults={[
            { pageIndex: 0, textItems: [{ str: 'result 1', matches: [], transform: [], width: 100, height: 20, itemIndex: 0 }] },
            { pageIndex: 1, textItems: [{ str: 'result 2', matches: [], transform: [], width: 100, height: 20, itemIndex: 1 }] }
          ]}
          currentSearchIndex={0}
        />
      )

      // Look for navigation buttons (ChevronDown for next)
      const buttons = screen.getAllByRole('button')
      const nextButton = buttons.find(button => button.querySelector('svg'))
      if (nextButton) {
        await user.click(nextButton)
        expect(onNavigateResults).toHaveBeenCalledWith('next')
      } else {
        // If no navigation buttons, just verify the component renders
        expect(screen.getByRole('textbox')).toBeInTheDocument()
      }
    })

    it('supports legacy clear search callback', async () => {
      const user = userEvent.setup()
      const onClearSearch = vi.fn()
      
      render(
        <PDFSearch 
          {...defaultProps} 
          searchText="some text"
          onClearSearch={onClearSearch}
        />
      )
      
      const clearButton = screen.getByRole('button', { name: /clear/i })
      await user.click(clearButton)
      
      expect(onClearSearch).toHaveBeenCalled()
    })
  })

  describe('Search State Management', () => {
    it('shows loading state when searching', () => {
      render(<PDFSearch {...defaultProps} isSearching={true} />)

      // Loading state shows a spinner icon in the search input
      const loadingIcon = screen.getByRole('textbox').parentElement?.querySelector('svg')
      expect(loadingIcon).toBeInTheDocument()
    })

    it('highlights current search result', () => {
      const searchResults = [
        { pageIndex: 0, textItems: [{ str: 'result 1', matches: [], transform: [], width: 100, height: 20, itemIndex: 0 }] },
        { pageIndex: 1, textItems: [{ str: 'result 2', matches: [], transform: [], width: 100, height: 20, itemIndex: 1 }] }
      ]

      render(
        <PDFSearch
          {...defaultProps}
          searchResults={searchResults}
          currentSearchIndex={1}
          variant="enhanced"
          pdfDocument={{} as any}
          numPages={10}
          onPageSelect={vi.fn()}
        />
      )

      // The current result index is shown in the badge
      expect(screen.getByText(/2 of 2/i)).toBeInTheDocument()
    })

    it('handles empty search results', () => {
      render(
        <PDFSearch
          {...defaultProps}
          searchResults={[]}
          searchText="no results"
        />
      )

      // When there are no results, navigation controls should not be visible
      expect(screen.queryByRole('button', { name: /previous/i })).not.toBeInTheDocument()
      expect(screen.queryByRole('button', { name: /next/i })).not.toBeInTheDocument()

      // The search input should still be visible
      expect(screen.getByDisplayValue('no results')).toBeInTheDocument()
    })
  })

  describe('Keyboard Navigation', () => {
    it('handles Enter key for search', async () => {
      const user = userEvent.setup()
      const onSearch = vi.fn()

      render(<PDFSearch {...defaultProps} onSearch={onSearch} variant="simple" searchText="keyboard search" />)

      const searchInput = screen.getByRole('textbox')
      await user.type(searchInput, '{enter}')

      expect(onSearch).toHaveBeenCalledWith('keyboard search')
    })

    it('handles Escape key to close', async () => {
      const user = userEvent.setup()
      const onClose = vi.fn()

      render(<PDFSearch {...defaultProps} onClose={onClose} variant="simple" />)

      const searchInput = screen.getByRole('textbox')
      await user.type(searchInput, '{escape}')

      expect(onClose).toHaveBeenCalled()
    })
  })
})